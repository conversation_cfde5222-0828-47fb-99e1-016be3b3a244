/**
 * Monaco Editor 选项合并系统测试
 * 用于验证选项合并功能的正确性
 */

import { OptionsMerger } from './optionsMerger.js'

// 测试用例
const testCases = [
  {
    name: '基础 ACE 选项转换',
    aceOptions: {
      tabSize: 4,
      fontSize: 16,
      readOnly: true,
      showLineNumbers: true,
      enableBasicAutocompletion: true
    },
    expected: {
      tabSize: 4,
      fontSize: 16,
      readOnly: true,
      lineNumbers: 'on',
      quickSuggestions: { other: true, comments: false, strings: false }
    }
  },
  {
    name: 'ACE 主题映射',
    aceOptions: {
      theme: 'monokai'
    },
    expected: {
      theme: 'vs-dark'
    }
  },
  {
    name: '文本换行选项',
    aceOptions: {
      wrap: true
    },
    expected: {
      wordWrap: 'on'
    }
  },
  {
    name: '代码片段选项',
    aceOptions: {
      enableSnippets: false
    },
    expected: {
      snippetSuggestions: 'none'
    }
  },
  {
    name: '无效选项处理',
    aceOptions: {
      invalidOption: 'test',
      fontSize: -1,
      tabSize: 'invalid'
    },
    expected: {}
  }
]

/**
 * 运行测试
 */
export function runTests() {
  console.log('🧪 开始 Monaco Editor 选项合并系统测试')
  
  const merger = new OptionsMerger({ debug: true })
  let passedTests = 0
  let totalTests = testCases.length
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`)
    
    try {
      const result = merger.mergeOptions(testCase.aceOptions, {}, {})
      const passed = validateResult(result, testCase.expected)
      
      if (passed) {
        console.log('✅ 测试通过')
        passedTests++
      } else {
        console.log('❌ 测试失败')
        console.log('期望结果:', testCase.expected)
        console.log('实际结果:', result)
      }
      
      // 显示警告和建议
      const warnings = merger.getWarnings()
      const suggestions = merger.getSuggestions()
      
      if (warnings.length > 0) {
        console.log('⚠️ 警告:', warnings.map(w => w.message))
      }
      
      if (suggestions.length > 0) {
        console.log('💡 建议:', suggestions.map(s => s.message))
      }
      
      merger.clearMessages()
      
    } catch (error) {
      console.log('❌ 测试出错:', error.message)
    }
  })
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！')
  } else {
    console.log('🔧 部分测试失败，需要修复')
  }
  
  return { passed: passedTests, total: totalTests }
}

/**
 * 验证测试结果
 * @param {Object} result - 实际结果
 * @param {Object} expected - 期望结果
 * @returns {boolean} 是否通过
 */
function validateResult(result, expected) {
  for (const [key, expectedValue] of Object.entries(expected)) {
    if (!result.hasOwnProperty(key)) {
      console.log(`缺少属性: ${key}`)
      return false
    }
    
    if (typeof expectedValue === 'object' && expectedValue !== null) {
      if (!deepEqual(result[key], expectedValue)) {
        console.log(`属性 ${key} 不匹配:`, result[key], 'vs', expectedValue)
        return false
      }
    } else if (result[key] !== expectedValue) {
      console.log(`属性 ${key} 不匹配:`, result[key], 'vs', expectedValue)
      return false
    }
  }
  
  return true
}

/**
 * 深度比较对象
 * @param {*} a - 对象 A
 * @param {*} b - 对象 B
 * @returns {boolean} 是否相等
 */
function deepEqual(a, b) {
  if (a === b) return true
  
  if (typeof a !== 'object' || typeof b !== 'object' || a === null || b === null) {
    return false
  }
  
  const keysA = Object.keys(a)
  const keysB = Object.keys(b)
  
  if (keysA.length !== keysB.length) return false
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false
    if (!deepEqual(a[key], b[key])) return false
  }
  
  return true
}

/**
 * 性能测试
 */
export function runPerformanceTest() {
  console.log('\n⚡ 开始性能测试')
  
  const merger = new OptionsMerger({ debug: false })
  const iterations = 1000
  
  const testOptions = {
    tabSize: 4,
    fontSize: 14,
    readOnly: false,
    enableBasicAutocompletion: true,
    enableLiveAutocompletion: true,
    enableSnippets: true,
    wrap: true,
    theme: 'monokai'
  }
  
  const startTime = performance.now()
  
  for (let i = 0; i < iterations; i++) {
    merger.mergeOptions(testOptions, {}, {})
  }
  
  const endTime = performance.now()
  const totalTime = endTime - startTime
  const avgTime = totalTime / iterations
  
  console.log(`📈 性能测试结果:`)
  console.log(`- 总时间: ${totalTime.toFixed(2)}ms`)
  console.log(`- 平均时间: ${avgTime.toFixed(4)}ms/次`)
  console.log(`- 迭代次数: ${iterations}`)
  
  return { totalTime, avgTime, iterations }
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  window.testMonacoEditorOptions = {
    runTests,
    runPerformanceTest
  }
}
