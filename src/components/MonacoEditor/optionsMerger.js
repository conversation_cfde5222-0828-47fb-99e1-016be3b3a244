/**
 * Monaco Editor 选项合并工具
 * 提供智能的选项合并、验证和转换功能
 */

import { 
  ACE_TO_MONACO_MAPPING, 
  MONACO_DEFAULT_OPTIONS, 
  OPTION_PRIORITY,
  DEBUG_CONFIG 
} from './editorOptionsMapping.js'

/**
 * 选项合并器类
 */
export class OptionsMerger {
  constructor(options = {}) {
    this.debugMode = options.debug !== undefined ? options.debug : DEBUG_CONFIG.enabled
    this.logLevel = options.logLevel || DEBUG_CONFIG.logLevel
    this.warnings = []
    this.suggestions = []
  }

  /**
   * 合并编辑器选项
   * @param {Object} aceOptions - ACE Editor 选项
   * @param {Object} monacoOptions - Monaco Editor 选项
   * @param {Object} userOptions - 用户自定义选项
   * @returns {Object} 合并后的 Monaco Editor 选项
   */
  mergeOptions(aceOptions = {}, monacoOptions = {}, userOptions = {}) {
    this.warnings = []
    this.suggestions = []
    
    this.log('info', '开始选项合并过程', { aceOptions, monacoOptions, userOptions })
    
    // 步骤 1: 转换 ACE 选项到 Monaco 选项
    const convertedAceOptions = this.convertAceOptions(aceOptions)
    
    // 步骤 2: 按优先级合并选项
    const mergedOptions = this.mergeByPriority({
      [OPTION_PRIORITY.DEFAULT]: MONACO_DEFAULT_OPTIONS,
      [OPTION_PRIORITY.ACE_COMPATIBLE]: convertedAceOptions,
      [OPTION_PRIORITY.USER_CUSTOM]: userOptions,
      [OPTION_PRIORITY.MONACO_NATIVE]: monacoOptions
    })
    
    // 步骤 3: 验证最终选项
    const validatedOptions = this.validateOptions(mergedOptions)
    
    // 步骤 4: 输出调试信息
    this.outputDebugInfo(validatedOptions)
    
    return validatedOptions
  }

  /**
   * 转换 ACE 选项到 Monaco 选项
   * @param {Object} aceOptions - ACE Editor 选项
   * @returns {Object} 转换后的 Monaco 选项
   */
  convertAceOptions(aceOptions) {
    const converted = {}
    
    for (const [aceKey, aceValue] of Object.entries(aceOptions)) {
      const mapping = ACE_TO_MONACO_MAPPING[aceKey]
      
      if (!mapping) {
        this.addWarning(`未知的 ACE 选项: ${aceKey}`, aceKey, aceValue)
        continue
      }
      
      try {
        // 验证选项值
        if (mapping.validator && !mapping.validator(aceValue)) {
          this.addWarning(`ACE 选项 ${aceKey} 的值无效: ${aceValue}`, aceKey, aceValue)
          continue
        }
        
        // 转换选项值
        const monacoValue = mapping.transform ? mapping.transform(aceValue) : aceValue
        converted[mapping.target] = monacoValue
        
        this.log('debug', `ACE 选项转换: ${aceKey} -> ${mapping.target}`, { 
          aceValue, 
          monacoValue 
        })
        
      } catch (error) {
        this.addWarning(`转换 ACE 选项 ${aceKey} 时出错: ${error.message}`, aceKey, aceValue)
      }
    }
    
    return converted
  }

  /**
   * 按优先级合并选项
   * @param {Object} optionsByPriority - 按优先级分组的选项
   * @returns {Object} 合并后的选项
   */
  mergeByPriority(optionsByPriority) {
    const merged = {}
    
    // 按优先级从低到高合并
    const priorities = Object.keys(optionsByPriority).sort((a, b) => a - b)
    
    for (const priority of priorities) {
      const options = optionsByPriority[priority]
      Object.assign(merged, options)
      
      this.log('debug', `合并优先级 ${priority} 的选项`, options)
    }
    
    return merged
  }

  /**
   * 验证选项
   * @param {Object} options - 要验证的选项
   * @returns {Object} 验证后的选项
   */
  validateOptions(options) {
    const validated = { ...options }
    
    // 基础类型验证
    const typeValidations = {
      fontSize: (value) => typeof value === 'number' && value > 0,
      tabSize: (value) => typeof value === 'number' && value > 0,
      readOnly: (value) => typeof value === 'boolean',
      wordWrap: (value) => ['off', 'on', 'wordWrapColumn', 'bounded'].includes(value),
      lineNumbers: (value) => ['on', 'off', 'relative', 'interval'].includes(value)
    }
    
    for (const [key, validator] of Object.entries(typeValidations)) {
      if (validated[key] !== undefined && !validator(validated[key])) {
        this.addWarning(`选项 ${key} 的值无效: ${validated[key]}`, key, validated[key])
        delete validated[key]
      }
    }
    
    return validated
  }

  /**
   * 添加警告
   * @param {string} message - 警告消息
   * @param {string} key - 选项键
   * @param {*} value - 选项值
   */
  addWarning(message, key, value) {
    const warning = { message, key, value, timestamp: Date.now() }
    this.warnings.push(warning)
    this.log('warn', message, { key, value })
  }

  /**
   * 添加建议
   * @param {string} message - 建议消息
   * @param {string} key - 选项键
   * @param {*} suggested - 建议值
   */
  addSuggestion(message, key, suggested) {
    const suggestion = { message, key, suggested, timestamp: Date.now() }
    this.suggestions.push(suggestion)
    this.log('info', message, { key, suggested })
  }

  /**
   * 输出调试信息
   * @param {Object} finalOptions - 最终选项
   */
  outputDebugInfo(finalOptions) {
    if (!this.debugMode) return
    
    this.log('info', '选项合并完成', finalOptions)
    
    if (this.warnings.length > 0) {
      this.log('warn', `发现 ${this.warnings.length} 个警告`, this.warnings)
    }
    
    if (this.suggestions.length > 0) {
      this.log('info', `提供 ${this.suggestions.length} 个建议`, this.suggestions)
    }
  }

  /**
   * 日志输出
   * @param {string} level - 日志级别
   * @param {string} message - 消息
   * @param {*} data - 数据
   */
  log(level, message, data) {
    if (!this.debugMode) return
    
    const levels = ['debug', 'info', 'warn', 'error']
    const currentLevelIndex = levels.indexOf(this.logLevel)
    const messageLevelIndex = levels.indexOf(level)
    
    if (messageLevelIndex >= currentLevelIndex) {
      const prefix = '[MonacoEditor OptionsMerger]'
      console[level](`${prefix} ${message}`, data || '')
    }
  }

  /**
   * 获取警告列表
   * @returns {Array} 警告列表
   */
  getWarnings() {
    return [...this.warnings]
  }

  /**
   * 获取建议列表
   * @returns {Array} 建议列表
   */
  getSuggestions() {
    return [...this.suggestions]
  }

  /**
   * 清除警告和建议
   */
  clearMessages() {
    this.warnings = []
    this.suggestions = []
  }
}
