# Monaco Editor 智能选项合并系统

## 概述

这是一个为 Monaco Editor 组件设计的智能选项合并系统，提供了完整的 ACE Editor 选项到 Monaco Editor 选项的转换和合并功能。

## 特性

- ✅ **完整的选项映射** - 支持常用的 ACE Editor 选项到 Monaco Editor 的转换
- ✅ **智能优先级处理** - 按优先级合并选项，避免冲突
- ✅ **选项验证** - 自动验证选项值的有效性
- ✅ **调试支持** - 开发模式下提供详细的转换日志
- ✅ **向后兼容** - 完全兼容现有的使用方式
- ✅ **性能优化** - 高效的选项合并算法

## 使用方法

### 基础使用

```vue
<template>
  <MonacoEditor
    v-model="code"
    language="javascript"
    :options="editorOptions"
    @init="handleEditorInit"
  />
</template>

<script>
import MonacoEditor from '@/components/MonacoEditor.vue'

export default {
  components: { MonacoEditor },
  data() {
    return {
      code: 'console.log("Hello World!")',
      editorOptions: {
        // ACE Editor 风格的选项（会自动转换）
        tabSize: 4,
        fontSize: 16,
        readOnly: false,
        enableBasicAutocompletion: true,
        enableLiveAutocompletion: true,
        enableSnippets: true,
        wrap: true,
        theme: 'monokai',
        showLineNumbers: true,
        highlightActiveLine: true
      }
    }
  },
  methods: {
    handleEditorInit(editor) {
      console.log('编辑器初始化完成', editor)
      
      // 获取选项合并的警告和建议
      const warnings = this.$refs.editor.getOptionWarnings()
      const suggestions = this.$refs.editor.getOptionSuggestions()
      
      if (warnings.length > 0) {
        console.warn('选项警告:', warnings)
      }
      
      if (suggestions.length > 0) {
        console.info('选项建议:', suggestions)
      }
    }
  }
}
</script>
```

### 高级使用

```javascript
// 手动合并选项（用于调试）
const mergedOptions = this.$refs.editor.mergeOptionsManually(
  { tabSize: 4, fontSize: 16 },  // ACE 选项
  { wordWrap: 'on' },            // Monaco 选项
  { theme: 'vs-dark' }           // 用户选项
)

console.log('合并后的选项:', mergedOptions)
```

## 支持的选项映射

### 基础编辑器选项

| ACE 选项 | Monaco 选项 | 说明 |
|---------|------------|------|
| `tabSize` | `tabSize` | Tab 键的空格数 |
| `fontSize` | `fontSize` | 字体大小 |
| `readOnly` | `readOnly` | 只读模式 |
| `useSoftTabs` | `insertSpaces` | 使用空格代替 Tab |

### 显示相关选项

| ACE 选项 | Monaco 选项 | 说明 |
|---------|------------|------|
| `showLineNumbers` | `lineNumbers` | 显示行号 |
| `showGutter` | `glyphMargin` | 显示边距 |
| `showPrintMargin` | `rulers` | 显示打印边距 |
| `highlightActiveLine` | `renderLineHighlight` | 高亮当前行 |

### 自动完成选项

| ACE 选项 | Monaco 选项 | 说明 |
|---------|------------|------|
| `enableBasicAutocompletion` | `quickSuggestions` | 基础自动完成 |
| `enableLiveAutocompletion` | `suggestOnTriggerCharacters` | 实时自动完成 |
| `enableSnippets` | `snippetSuggestions` | 代码片段建议 |

### 文本处理选项

| ACE 选项 | Monaco 选项 | 说明 |
|---------|------------|------|
| `wrap` | `wordWrap` | 文本换行 |
| `scrollPastEnd` | `scrollBeyondLastLine` | 滚动超过最后一行 |
| `foldStyle` | `folding` | 代码折叠 |

### 主题映射

| ACE 主题 | Monaco 主题 |
|---------|------------|
| `monokai` | `vs-dark` |
| `github` | `vs` |
| `tomorrow` | `vs` |
| `tomorrow_night` | `vs-dark` |
| `solarized_dark` | `vs-dark` |
| `solarized_light` | `vs` |

## 选项优先级

系统按以下优先级合并选项（数字越大优先级越高）：

1. **默认选项** (优先级: 1) - 系统默认配置
2. **ACE 兼容选项** (优先级: 2) - 从 ACE 选项转换而来
3. **用户自定义选项** (优先级: 3) - 用户直接传入的选项
4. **Monaco 原生选项** (优先级: 4) - Monaco Editor 原生选项

## 调试功能

### 开发模式日志

在开发环境下，系统会自动输出详细的选项合并日志：

```javascript
// 控制台输出示例
[MonacoEditor OptionsMerger] 开始选项合并过程 {aceOptions: {...}, monacoOptions: {...}}
[MonacoEditor OptionsMerger] ACE 选项转换: tabSize -> tabSize {aceValue: 4, monacoValue: 4}
[MonacoEditor OptionsMerger] 合并优先级 1 的选项 {...}
[MonacoEditor OptionsMerger] 选项合并完成 {...}
```

### 获取警告和建议

```javascript
// 获取选项合并过程中的警告
const warnings = this.$refs.editor.getOptionWarnings()
warnings.forEach(warning => {
  console.warn(`警告: ${warning.message}`, warning)
})

// 获取选项优化建议
const suggestions = this.$refs.editor.getOptionSuggestions()
suggestions.forEach(suggestion => {
  console.info(`建议: ${suggestion.message}`, suggestion)
})

// 清除消息
this.$refs.editor.clearOptionMessages()
```

## 测试

运行测试以验证选项合并功能：

```javascript
import { runTests, runPerformanceTest } from '@/components/MonacoEditor/test-options-merger.js'

// 运行功能测试
runTests()

// 运行性能测试
runPerformanceTest()
```

## 扩展

### 添加新的选项映射

在 `editorOptionsMapping.js` 中添加新的映射规则：

```javascript
export const ACE_TO_MONACO_MAPPING = {
  // 现有映射...
  
  // 新增映射
  newAceOption: {
    target: 'newMonacoOption',
    type: 'boolean',
    validator: (value) => typeof value === 'boolean',
    transform: (value) => !!value,
    defaultValue: false
  }
}
```

### 自定义验证器

```javascript
// 在映射配置中添加自定义验证逻辑
customOption: {
  target: 'customMonacoOption',
  type: 'string',
  validator: (value) => {
    // 自定义验证逻辑
    return typeof value === 'string' && value.length > 0
  },
  transform: (value) => {
    // 自定义转换逻辑
    return value.toLowerCase()
  }
}
```

## 注意事项

1. **性能考虑** - 选项合并在每次选项变更时都会执行，建议避免频繁修改选项
2. **类型安全** - 系统会验证选项类型，无效的选项会被忽略并产生警告
3. **向后兼容** - 现有的使用方式完全兼容，无需修改现有代码
4. **调试模式** - 生产环境下调试日志会被自动禁用以提高性能

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的 ACE 到 Monaco 选项映射
- 实现智能优先级合并
- 添加选项验证和调试功能
