/**
 * Monaco Editor 选项映射配置
 * 提供 ACE Editor 选项到 Monaco Editor 选项的完整映射关系
 */

// ACE Editor 到 Monaco Editor 的选项映射表
export const ACE_TO_MONACO_MAPPING = {
  // 基础编辑器选项
  tabSize: {
    target: 'tabSize',
    type: 'number',
    validator: (value) => typeof value === 'number' && value > 0,
    defaultValue: 2
  },
  
  fontSize: {
    target: 'fontSize',
    type: 'number',
    validator: (value) => typeof value === 'number' && value > 0,
    defaultValue: 14
  },
  
  readOnly: {
    target: 'readOnly',
    type: 'boolean',
    validator: (value) => typeof value === 'boolean',
    transform: (value) => !!value
  },
  
  // 显示相关选项
  showLineNumbers: {
    target: 'lineNumbers',
    type: 'string|boolean',
    validator: (value) => ['on', 'off', 'relative', 'interval', true, false].includes(value),
    transform: (value) => {
      if (typeof value === 'boolean') {
        return value ? 'on' : 'off'
      }
      return value
    },
    defaultValue: 'on'
  },
  
  showGutter: {
    target: 'glyphMargin',
    type: 'boolean',
    validator: (value) => typeof value === 'boolean',
    transform: (value) => !!value,
    defaultValue: false
  },
  
  showPrintMargin: {
    target: 'rulers',
    type: 'boolean',
    validator: (value) => typeof value === 'boolean',
    transform: (value) => value ? [80] : [],
    defaultValue: false
  },
  
  highlightActiveLine: {
    target: 'renderLineHighlight',
    type: 'boolean|string',
    validator: (value) => typeof value === 'boolean' || ['none', 'gutter', 'line', 'all'].includes(value),
    transform: (value) => {
      if (typeof value === 'boolean') {
        return value ? 'line' : 'none'
      }
      return value
    },
    defaultValue: 'line'
  },
  
  // 自动完成和建议
  enableBasicAutocompletion: {
    target: 'quickSuggestions',
    type: 'boolean|object',
    validator: (value) => typeof value === 'boolean' || typeof value === 'object',
    transform: (value) => {
      if (typeof value === 'boolean') {
        return value ? { other: true, comments: false, strings: false } : false
      }
      return value
    },
    defaultValue: true
  },
  
  enableLiveAutocompletion: {
    target: 'suggestOnTriggerCharacters',
    type: 'boolean',
    validator: (value) => typeof value === 'boolean',
    transform: (value) => !!value,
    defaultValue: true
  },
  
  enableSnippets: {
    target: 'snippetSuggestions',
    type: 'boolean|string',
    validator: (value) => typeof value === 'boolean' || ['top', 'bottom', 'inline', 'none'].includes(value),
    transform: (value) => {
      if (typeof value === 'boolean') {
        return value ? 'top' : 'none'
      }
      return value
    },
    defaultValue: 'top'
  },
  
  // 文本换行
  wrap: {
    target: 'wordWrap',
    type: 'boolean|string',
    validator: (value) => typeof value === 'boolean' || ['off', 'on', 'wordWrapColumn', 'bounded'].includes(value),
    transform: (value) => {
      if (typeof value === 'boolean') {
        return value ? 'on' : 'off'
      }
      return value
    },
    defaultValue: 'on'
  },
  
  // 主题相关
  theme: {
    target: 'theme',
    type: 'string',
    validator: (value) => typeof value === 'string',
    transform: (value) => {
      // ACE 主题到 Monaco 主题的映射
      const themeMapping = {
        'monokai': 'vs-dark',
        'github': 'vs',
        'tomorrow': 'vs',
        'tomorrow_night': 'vs-dark',
        'twilight': 'vs-dark',
        'textmate': 'vs',
        'solarized_dark': 'vs-dark',
        'solarized_light': 'vs'
      }
      return themeMapping[value] || value
    }
  },
  
  // 缩进相关
  useSoftTabs: {
    target: 'insertSpaces',
    type: 'boolean',
    validator: (value) => typeof value === 'boolean',
    transform: (value) => !!value,
    defaultValue: true
  },
  
  // 滚动相关
  scrollPastEnd: {
    target: 'scrollBeyondLastLine',
    type: 'boolean',
    validator: (value) => typeof value === 'boolean',
    transform: (value) => !!value,
    defaultValue: false
  },
  
  // 代码折叠
  foldStyle: {
    target: 'folding',
    type: 'boolean|string',
    validator: (value) => typeof value === 'boolean' || typeof value === 'string',
    transform: (value) => {
      if (typeof value === 'boolean') {
        return value
      }
      // ACE 的 foldStyle 转换为 Monaco 的 folding
      return value !== 'manual'
    },
    defaultValue: true
  }
}

// Monaco Editor 默认选项
export const MONACO_DEFAULT_OPTIONS = {
  automaticLayout: true,
  scrollBeyondLastLine: false,
  minimap: { enabled: false },
  fontSize: 14,
  tabSize: 2,
  insertSpaces: true,
  wordWrap: 'on',
  lineNumbers: 'on',
  glyphMargin: false,
  folding: true,
  lineDecorationsWidth: 10,
  lineNumbersMinChars: 3,
  renderLineHighlight: 'line',
  quickSuggestions: true,
  suggestOnTriggerCharacters: true,
  snippetSuggestions: 'top'
}

// 选项优先级定义
export const OPTION_PRIORITY = {
  MONACO_NATIVE: 4,    // Monaco 原生选项（最高优先级）
  USER_CUSTOM: 3,      // 用户自定义选项
  ACE_COMPATIBLE: 2,   // ACE 兼容选项
  DEFAULT: 1           // 默认选项（最低优先级）
}

// 调试模式配置
export const DEBUG_CONFIG = {
  enabled: process.env.NODE_ENV === 'development',
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
  showWarnings: true,
  showSuggestions: true
}
